MODBUS_RTU=[0x00]
MODBUS_HEARD=[0x00,0x00,0x00]
MODBUS_LENGTH=[0x00]
LENGTH_HEX1=[0x00]
LENGTH1="0"
LENGTH2="0"
crc_char1="0"
crc_char2="0"
crc_char3="0"
crc_char4="0"
HEX_STRING="123456789ABCDEF"
modbusheard=0
modbusheard_char1="0"
modbusheard_char2="0"
modbusheard_char3="0"
modbusheard_char4="0"
crc1=0
crc2=0
crc3=0
crc4=0
RECV SOCK netp
    MODBUS_SOCK=INPUT
	MODBUS_SOCK_LENGTH=MODBUS_SOCK.length()
	MODBUS_SOCK=MODBUS_SOCK.subString(6,MODBUS_SOCK_LENGTH)
	crc=CALCRC16(MODBUS_SOCK)
	crc1=crc/4096
	crc2=(crc-(crc1*4096))/256
        crc3=(crc-(crc1*4096+crc2*256))/16
	crc4=crc-((crc/16)*16)
        IF(crc1>0)
        crc_char1=HEX_STRING.subString(crc1-1,crc1)
        END
        IF(crc2>0)
        crc_char2=HEX_STRING.subString(crc2-1,crc2)
        END
        IF(crc3>0)
        crc_char3=HEX_STRING.subString(crc3-1,crc3)
        END
        IF(crc4>0)
        crc_char4=HEX_STRING.subString(crc4-1,crc4)
        END
       crc_char12=crc_char1+crc_char2+crc_char3+crc_char4
	   crc__HEX=crc_char12.stringHex()
	   MODBUS_SOCK=MODBUS_SOCK+crc__HEX
       SEND(UART,uart0,MODBUS_SOCK)
       crc_char1="0"
       crc_char2="0"
      crc_char3="0"
       crc_char4="0"
END
RECV UART uart0
    MODBUS_RTU=INPUT
    LENGTH=MODBUS_RTU.length()
    LENGTH=LENGTH-2
	LENGTH_quzheng=LENGTH/0x10
LENGTH_QUYU=LENGTH-(LENGTH_quzheng*16)
IF(LENGTH_quzheng>0)
LENGTH1=HEX_STRING.subString(LENGTH_quzheng-1,LENGTH_quzheng)
END
IF(LENGTH_QUYU>0)
LENGTH2=HEX_STRING.subString(LENGTH_QUYU-1,LENGTH_QUYU)
END
LENGTH_HEX=LENGTH1+LENGTH2
LENGTH_HEX1=LENGTH_HEX.stringHex()
    MODBUS_CONTENT=MODBUS_RTU.subString(0,LENGTH)
    MODBUS_LENGTH=LENGTH.prtString()
	MODBUS_LENGTH=MODBUS_LENGTH.stringHex()
	modbusheard=modbusheard+1
        IF(modbusheard>65535)
         modbusheard=0
        END
	modbusheard1=modbusheard/4096
	modbusheard2=(modbusheard-(modbusheard1*4096))/256
        modbusheard3=(modbusheard-(modbusheard1*4096+modbusheard2*256))/16
	modbusheard4=modbusheard-((modbusheard/16)*16)
        IF(modbusheard1>0)
        modbusheard_char1=HEX_STRING.subString(modbusheard1-1,modbusheard1)
        END
        IF(modbusheard2>0)
        modbusheard_char2=HEX_STRING.subString(modbusheard2-1,modbusheard2)
        END
        IF(modbusheard3>0)
        modbusheard_char3=HEX_STRING.subString(modbusheard3-1,modbusheard3)
        END
        IF(modbusheard4>0)
        modbusheard_char4=HEX_STRING.subString(modbusheard4-1,modbusheard4)
        END
       modbusheard_char12=modbusheard_char3+modbusheard_char4+modbusheard_char1+modbusheard_char2
	   modbusheard__HEX=modbusheard_char12.stringHex()
   MODBUS_TCP=modbusheard__HEX+MODBUS_HEARD+LENGTH_HEX1+MODBUS_CONTENT
   SEND(SOCK,netp,MODBUS_TCP)
    modbusheard1=0
    modbusheard2=0
    modbusheard3=0
    modbusheard4=0
	LENGTH1="0"
    LENGTH2="0"
END