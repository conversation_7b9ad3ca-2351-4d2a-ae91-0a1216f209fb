#
# Copyright (c) 2004, 2016, Oracle and/or its affiliates. All rights reserved.
# ORACLE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
#

error.internal.badmsg=\uB0B4\uBD80 \uC624\uB958\uAC00 \uBC1C\uC0DD\uD588\uC2B5\uB2C8\uB2E4. \uC54C \uC218 \uC5C6\uB294 \uBA54\uC2DC\uC9C0\uC785\uB2C8\uB2E4.
error.badinst.nojre=\uC124\uCE58\uAC00 \uC798\uBABB\uB418\uC5C8\uC2B5\uB2C8\uB2E4. \uAD6C\uC131 \uD30C\uC77C\uC5D0\uC11C JRE\uB97C \uCC3E\uC744 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
error.launch.execv=Java Web Start(execv)\uB97C \uD638\uCD9C\uD558\uB294 \uC911 \uC624\uB958\uAC00 \uBC1C\uC0DD\uD588\uC2B5\uB2C8\uB2E4.
error.launch.sysexec=Java Web Start(SysExec)\uB97C \uD638\uCD9C\uD558\uB294 \uC911 \uC624\uB958\uAC00 \uBC1C\uC0DD\uD588\uC2B5\uB2C8\uB2E4. 
error.listener.failed=\uC2A4\uD50C\uB798\uC2DC: sysCreateListenerSocket\uC744 \uC2E4\uD328\uD588\uC2B5\uB2C8\uB2E4.
error.accept.failed=\uC2A4\uD50C\uB798\uC2DC: \uC2B9\uC778\uC744 \uC2E4\uD328\uD588\uC2B5\uB2C8\uB2E4.
error.recv.failed=\uC2A4\uD50C\uB798\uC2DC: recv\uB97C \uC2E4\uD328\uD588\uC2B5\uB2C8\uB2E4.
error.invalid.port=\uC2A4\uD50C\uB798\uC2DC: \uC801\uD569\uD55C \uD3EC\uD2B8\uB97C \uBCF5\uC6D0\uD558\uC9C0 \uBABB\uD588\uC2B5\uB2C8\uB2E4.
error.read=\uBC84\uD37C \uB05D\uC744 \uC9C0\uB098\uC11C \uC77D\uC5C8\uC2B5\uB2C8\uB2E4.
error.xmlparsing=XML \uAD6C\uBB38 \uBD84\uC11D \uC624\uB958: \uC798\uBABB\uB41C \uD1A0\uD070 \uC720\uD615\uC774 \uBC1C\uACAC\uB418\uC5C8\uC2B5\uB2C8\uB2E4.
error.splash.exit=Java Web Start \uC2A4\uD50C\uB798\uC2DC \uD654\uBA74 \uCC98\uB9AC\uB97C \uC885\uB8CC\uD558\uB294 \uC911...\n
# "Last WinSock Error" means the error message for the last operation that failed.
error.winsock=\t\uB9C8\uC9C0\uB9C9 WinSock \uC624\uB958: 
error.winsock.load=winsock.dll\uC744 \uB85C\uB4DC\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
error.winsock.start=WSAStartup\uC744 \uC2E4\uD328\uD588\uC2B5\uB2C8\uB2E4.
error.badinst.nohome=\uC798\uBABB\uB41C \uC124\uCE58: JAVAWS_HOME\uC774 \uC124\uC815\uB418\uC9C0 \uC54A\uC558\uC2B5\uB2C8\uB2E4. 
error.splash.noimage=\uC2A4\uD50C\uB798\uC2DC: \uC2A4\uD50C\uB798\uC2DC \uD654\uBA74 \uC774\uBBF8\uC9C0\uB97C \uB85C\uB4DC\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
error.splash.socket=\uC2A4\uD50C\uB798\uC2DC: \uC11C\uBC84 \uC18C\uCF13\uC744 \uC2E4\uD328\uD588\uC2B5\uB2C8\uB2E4.
error.splash.cmnd=\uC2A4\uD50C\uB798\uC2DC: \uC54C \uC218 \uC5C6\uB294 \uBA85\uB839\uC785\uB2C8\uB2E4.
error.splash.port=\uC2A4\uD50C\uB798\uC2DC: \uD3EC\uD2B8\uAC00 \uC9C0\uC815\uB418\uC9C0 \uC54A\uC558\uC2B5\uB2C8\uB2E4.
error.splash.send=\uC2A4\uD50C\uB798\uC2DC: \uC804\uC1A1\uC744 \uC2E4\uD328\uD588\uC2B5\uB2C8\uB2E4.
error.splash.timer=\uC2A4\uD50C\uB798\uC2DC: \uC885\uB8CC \uD0C0\uC774\uBA38\uB97C \uC0DD\uC131\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
error.splash.x11.open=\uC2A4\uD50C\uB798\uC2DC: X11 \uB514\uC2A4\uD50C\uB808\uC774\uB97C \uC5F4 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
error.splash.x11.connect=\uC2A4\uD50C\uB798\uC2DC: X11 \uC811\uC18D\uC744 \uC2E4\uD328\uD588\uC2B5\uB2C8\uB2E4.
# Javaws usage: '\' is a joining of two sentence,which are connected including
# the invisible character '\n'.
message.javaws.usage=\n\uC0AC\uC6A9\uBC95:\tjavaws [run-options] <jnlp-file>\t\n\tjavaws [control-options]\t\t\n\n\uC5EC\uAE30\uC11C run-options\uB294 \uB2E4\uC74C\uACFC \uAC19\uC2B5\uB2C8\uB2E4.\t\t\t\n-verbose       \t\uCD94\uAC00 \uCD9C\uB825\uC744 \uD45C\uC2DC\uD569\uB2C8\uB2E4.\t\n-offline       \t\uC624\uD504\uB77C\uC778 \uBAA8\uB4DC\uB85C \uC560\uD50C\uB9AC\uCF00\uC774\uC158\uC744 \uC2E4\uD589\uD569\uB2C8\uB2E4.\t\n-system        \t\uC2DC\uC2A4\uD15C \uCE90\uC2DC\uC5D0\uC11C\uB9CC \uC560\uD50C\uB9AC\uCF00\uC774\uC158\uC744 \uC2E4\uD589\uD569\uB2C8\uB2E4.\n-Xnosplash     \t\uC2A4\uD50C\uB798\uC2DC \uD654\uBA74\uC744 \uD45C\uC2DC\uD558\uC9C0 \uC54A\uACE0 \uC2E4\uD589\uD569\uB2C8\uB2E4.\t\n-J<option>     \tvm\uC5D0 \uC635\uC158\uC744 \uC81C\uACF5\uD569\uB2C8\uB2E4.\t\n-wait          \tJava \uD504\uB85C\uC138\uC2A4\uB97C \uC2DC\uC791\uD558\uACE0 \uC885\uB8CC\uB420 \uB54C\uAE4C\uC9C0 \uAE30\uB2E4\uB9BD\uB2C8\uB2E4.\t\n\ncontrol-options\uB294 \uB2E4\uC74C\uACFC \uAC19\uC2B5\uB2C8\uB2E4.\t\n-viewer        \tJava \uC81C\uC5B4\uD310\uC5D0\uC11C \uCE90\uC2DC \uBDF0\uC5B4\uB97C \uD45C\uC2DC\uD569\uB2C8\uB2E4.\n-clearcache    \t\uCE90\uC2DC\uC5D0\uC11C \uC124\uCE58\uB418\uC9C0 \uC54A\uC740 \uBAA8\uB4E0 \uC560\uD50C\uB9AC\uCF00\uC774\uC158\uC744 \uC81C\uAC70\uD569\uB2C8\uB2E4.\n-uninstall     \t\uCE90\uC2DC\uC5D0\uC11C \uBAA8\uB4E0 \uC560\uD50C\uB9AC\uCF00\uC774\uC158\uC744 \uC81C\uAC70\uD569\uB2C8\uB2E4.\n-uninstall <jnlp-file>              \t\uCE90\uC2DC\uC5D0\uC11C \uC560\uD50C\uB9AC\uCF00\uC774\uC158\uC744 \uC81C\uAC70\uD569\uB2C8\uB2E4.\t\n-import [import-options] <jnlp-file>\t\uCE90\uC2DC\uB85C \uC560\uD50C\uB9AC\uCF00\uC774\uC158\uC744 \uC784\uD3EC\uD2B8\uD569\uB2C8\uB2E4.\t\t\n\nimport-options\uB294 \uB2E4\uC74C\uACFC \uAC19\uC2B5\uB2C8\uB2E4.\t\t\t\t\t\t\n-silent        \t\uC0AC\uC6A9\uC790 \uC778\uD130\uD398\uC774\uC2A4 \uC5C6\uC774 \uC790\uB3D9\uC73C\uB85C \uC784\uD3EC\uD2B8\uD569\uB2C8\uB2E4.\t\n-system        \t\uC2DC\uC2A4\uD15C \uCE90\uC2DC\uB85C \uC560\uD50C\uB9AC\uCF00\uC774\uC158\uC744 \uC784\uD3EC\uD2B8\uD569\uB2C8\uB2E4.\t\n-codebase <url>\t\uC81C\uACF5\uB41C \uCF54\uB4DC\uBCA0\uC774\uC2A4\uC5D0\uC11C \uB9AC\uC18C\uC2A4\uB97C \uAC80\uC0C9\uD569\uB2C8\uB2E4.\t\n-shortcut      \t\uC0AC\uC6A9\uC790\uAC00 \uD504\uB86C\uD504\uD2B8\uB97C \uD5C8\uC6A9\uD55C \uAC83\uC73C\uB85C \uAC04\uC8FC\uD558\uC5EC \uB2E8\uCD95\uD0A4\uB97C \uC124\uCE58\uD569\uB2C8\uB2E4.\t\n-association   \t\uC0AC\uC6A9\uC790\uAC00 \uD504\uB86C\uD504\uD2B8\uB97C \uD5C8\uC6A9\uD55C \uAC83\uC73C\uB85C \uAC04\uC8FC\uD558\uC5EC \uC5F0\uAD00\uC744 \uC124\uCE58\uD569\uB2C8\uB2E4.\t\n\n
