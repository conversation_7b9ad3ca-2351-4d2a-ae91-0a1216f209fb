low1=[0x01,0x00]
low2=[0x02,0x00]
high1=[0x01,0x01]
high2=[0x02,0x01]
get=[0x03,0x03]

RECV SOCK netp
	  IF (INPUT==get)
		    di1=GETHW(GPIO, 3)
		    IF(di1==0)
		    	SEND(UART,uart0,"di1 0\r\n")
		    ELSE
			SEND(UART,uart0,"di1 1\r\n")
		    END

		    di2=GETHW(GPIO, 41)
		    IF (di2==0)
		    	SEND(UART,uart0,"di2 0\r\n")
		    ELSE
			SEND(UART,uart0,"di2 1\r\n")
		    END

	          di3=GETHW(GPIO, 40)
		    IF (di3==0)
		    	SEND(UART,uart0,"di3 0\r\n")
		    ELSE
			SEND(UART,uart0,"di3 1\r\n")
		    END

		    do1=GETHW(GPIO, 0)
		    IF (do1==0)
		    	SEND(UART,uart0,"do1 0\r\n")
		    ELSE
			SEND(UART,uart0,"do1 1\r\n")
		    END

	    	    do2=GETHW(GPIO, 1)
		    IF (do2==0)
		    	SEND(UART,uart0,"do2 0\r\n")
		    ELSE
			SEND(UART,uart0,"do2 1\r\n")
		    END

                RETURN(FALSE)
        END


        IF (INPUT==high1)
                SETHW(GPIO, OUT, 0, 1)
                RETURN(FALSE)
        END

        IF (INPUT==high2)
                SETHW(GPIO, OUT, 1, 1)
                RETURN(FALSE)
        END

        IF (INPUT==low1)
                SETHW(GPIO, OUT, 0, 0)
                RETURN(FALSE)
        END

        IF (INPUT==low2)
                SETHW(GPIO, OUT, 1, 0)
                RETURN(FALSE)
        END

        OUTPUT=INPUT
        RETURN(TRUE)

END