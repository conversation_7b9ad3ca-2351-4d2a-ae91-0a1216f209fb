#
# Copyright (c) 2004, 2013, Oracle and/or its affiliates. All rights reserved.
# ORACLE PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
#

error.internal.badmsg=Error interno, mensaje desconocido
error.badinst.nojre=Instalaci\u00F3n incorrecta. No se ha encontrado JRE en el archivo de configuraci\u00F3n
error.launch.execv=Se ha encontrado un error al llamar a Java Web Start (execv)
error.launch.sysexec=Se ha encontrado un error al llamar a Java Web Start (SysExec) 
error.listener.failed=Pantalla de Presentaci\u00F3n: fallo de sysCreateListenerSocket
error.accept.failed=Pantalla de Presentaci\u00F3n: fallo de accept
error.recv.failed=Pantalla de Presentaci\u00F3n: fallo de recv
error.invalid.port=Pantalla de Presentaci\u00F3n: no se ha activado un puerto v\u00E1lido
error.read=Lectura m\u00E1s all\u00E1 del final del buffer
error.xmlparsing=Error de an\u00E1lisis de XML: se ha encontrado un tipo de token no v\u00E1lido
error.splash.exit=Saliendo del proceso de la pantalla de presentaci\u00F3n de Java Web Start...\n
# "Last WinSock Error" means the error message for the last operation that failed.
error.winsock=\t\u00DAltimo Error de WinSock: 
error.winsock.load=No se ha podido cargar winsock.dll
error.winsock.start=Fallo de WSAStartup
error.badinst.nohome=Instalaci\u00F3n incorrecta: JAVAWS_HOME no definido 
error.splash.noimage=Presentaci\u00F3n: no se ha podido cargar la imagen de la pantalla de presentaci\u00F3n
error.splash.socket=Pantalla de Presentaci\u00F3n: fallo en el socket del servidor
error.splash.cmnd=Pantalla de Presentaci\u00F3n: comando no reconocido
error.splash.port=Pantalla de Presentaci\u00F3n: puerto no especificado
error.splash.send=Pantalla de Presentaci\u00F3n: fallo de send
error.splash.timer=Pantalla de Presentaci\u00F3n: no se ha podido crear el temporizador de apagado
error.splash.x11.open=Pantalla de Presentaci\u00F3n: no se ha podido abrir la pantalla X11
error.splash.x11.connect=Pantalla de Presentaci\u00F3n: fallo de conexi\u00F3n X11
# Javaws usage: '\' is a joining of two sentence,which are connected including
# the invisible character '\n'.
message.javaws.usage=\nSintaxis:\tjavaws [run-options] <archivo-jnlp>\t\n\tjavaws [control-options]\t\t\n\ndonde run-options incluye:\t\t\t\n-verbose       \tmostrar salida adicional\t\n-offline       \tejecutar la aplicaci\u00F3n en el modo fuera de l\u00EDnea\t\n-system        \tejecutar la aplicaci\u00F3n \u00FAnicamente desde la cach\u00E9 del sistema\n-Xnosplash     \tejecutar sin mostrar ninguna pantalla de presentaci\u00F3n\t\n-J<opci\u00F3n>     \tproporcione una opci\u00F3n a la VM\t\n-wait          \tiniciar un proceso java y esperar a que se cierre\t\n\ncontrol-options incluye:\t\n-viewer        \tmostrar el visor de la cach\u00E9 en el panel de control java\n-clearcache    \teliminar todas las aplicaciones no instaladas desde la cach\u00E9\n-uninstall     \teliminar todas las aplicaciones de la cach\u00E9\n-uninstall <archivo-jnlp>              \teliminar la aplicaci\u00F3n de la cach\u00E9\t\n-import [import-options] <archivo-jnlp>\timportar la aplicaci\u00F3n a la cach\u00E9\t\t\n\nimport-options incluye:\t\t\t\t\t\t\n-silent        \timportar de forma silenciosa (sin interfaz de usuario)\t\n-system        \timportar la aplicaci\u00F3n a la cach\u00E9 del sistema\t\n-codebase <url>\trecuperar los recursos del codebase correspondiente\t\n-shortcut      \tinstalar los accesos directos como si el usuario hubiera aceptado la petici\u00F3n\t\n-association   \tinstalar las asociaciones como si el usuario hubiera aceptado la petici\u00F3n\t\n\n
